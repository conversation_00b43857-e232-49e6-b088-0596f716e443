## workspace
# bitbucket.org/bertimus9/systemstat v0.5.0
## explicit; go 1.17
bitbucket.org/bertimus9/systemstat
# cel.dev/expr v0.24.0
## explicit; go 1.22.0
cel.dev/expr
# github.com/Azure/go-ansiterm v0.0.0-20230124172434-306776ec8161
## explicit; go 1.16
github.com/Azure/go-ansiterm
github.com/Azure/go-ansiterm/winterm
# github.com/JeffAshton/win_pdh v0.0.0-20161109143554-76bb4ee9f0ab
## explicit
github.com/JeffAshton/win_pdh
# github.com/MakeNowJust/heredoc v1.0.0
## explicit; go 1.12
github.com/MakeNowJust/heredoc
# github.com/Microsoft/go-winio v0.6.2
## explicit; go 1.21
github.com/Microsoft/go-winio
github.com/Microsoft/go-winio/internal/fs
github.com/Microsoft/go-winio/internal/socket
github.com/Microsoft/go-winio/internal/stringbuffer
github.com/Microsoft/go-winio/pkg/guid
# github.com/Microsoft/hnslib v0.1.1
## explicit; go 1.22.0
github.com/Microsoft/hnslib
github.com/Microsoft/hnslib/hcn
github.com/Microsoft/hnslib/internal/cni
github.com/Microsoft/hnslib/internal/hns
github.com/Microsoft/hnslib/internal/interop
github.com/Microsoft/hnslib/internal/regstate
github.com/Microsoft/hnslib/internal/runhcs
# github.com/NYTimes/gziphandler v1.1.1
## explicit; go 1.11
github.com/NYTimes/gziphandler
# github.com/antlr4-go/antlr/v4 v4.13.0
## explicit; go 1.20
github.com/antlr4-go/antlr/v4
# github.com/armon/circbuf v0.0.0-20190214190532-5111143e8da2
## explicit
github.com/armon/circbuf
# github.com/armon/go-socks5 v0.0.0-20160902184237-e75332964ef5
## explicit
github.com/armon/go-socks5
# github.com/beorn7/perks v1.0.1
## explicit; go 1.11
github.com/beorn7/perks/quantile
# github.com/blang/semver/v4 v4.0.0
## explicit; go 1.14
github.com/blang/semver/v4
# github.com/cenkalti/backoff/v4 v4.3.0
## explicit; go 1.18
github.com/cenkalti/backoff/v4
# github.com/cespare/xxhash/v2 v2.3.0
## explicit; go 1.11
github.com/cespare/xxhash/v2
# github.com/chai2010/gettext-go v1.0.2
## explicit; go 1.14
github.com/chai2010/gettext-go
github.com/chai2010/gettext-go/mo
github.com/chai2010/gettext-go/plural
github.com/chai2010/gettext-go/po
# github.com/container-storage-interface/spec v1.9.0
## explicit; go 1.18
github.com/container-storage-interface/spec/lib/go/csi
# github.com/containerd/containerd/api v1.8.0
## explicit; go 1.21
github.com/containerd/containerd/api/services/containers/v1
github.com/containerd/containerd/api/services/tasks/v1
github.com/containerd/containerd/api/services/version/v1
github.com/containerd/containerd/api/types
github.com/containerd/containerd/api/types/task
# github.com/containerd/errdefs v1.0.0
## explicit; go 1.20
github.com/containerd/errdefs
# github.com/containerd/errdefs/pkg v0.3.0
## explicit; go 1.22
github.com/containerd/errdefs/pkg/errgrpc
github.com/containerd/errdefs/pkg/internal/cause
github.com/containerd/errdefs/pkg/internal/types
# github.com/containerd/log v0.1.0
## explicit; go 1.20
github.com/containerd/log
# github.com/containerd/ttrpc v1.2.6
## explicit; go 1.19
github.com/containerd/ttrpc
# github.com/containerd/typeurl/v2 v2.2.2
## explicit; go 1.21
github.com/containerd/typeurl/v2
# github.com/coredns/caddy v1.1.1
## explicit; go 1.13
github.com/coredns/caddy/caddyfile
# github.com/coredns/corefile-migration v1.0.26
## explicit; go 1.14
github.com/coredns/corefile-migration/migration
github.com/coredns/corefile-migration/migration/corefile
# github.com/coreos/go-oidc v2.3.0+incompatible
## explicit
github.com/coreos/go-oidc
# github.com/coreos/go-semver v0.3.1
## explicit; go 1.8
github.com/coreos/go-semver/semver
# github.com/coreos/go-systemd/v22 v22.5.0
## explicit; go 1.12
github.com/coreos/go-systemd/v22/daemon
github.com/coreos/go-systemd/v22/dbus
github.com/coreos/go-systemd/v22/internal/dlopen
github.com/coreos/go-systemd/v22/journal
github.com/coreos/go-systemd/v22/util
# github.com/cpuguy83/go-md2man/v2 v2.0.6
## explicit; go 1.12
github.com/cpuguy83/go-md2man/v2/md2man
# github.com/cyphar/filepath-securejoin v0.4.1
## explicit; go 1.18
github.com/cyphar/filepath-securejoin
# github.com/davecgh/go-spew v1.1.1
## explicit
github.com/davecgh/go-spew/spew
# github.com/distribution/reference v0.6.0
## explicit; go 1.20
github.com/distribution/reference
# github.com/docker/go-units v0.5.0
## explicit
github.com/docker/go-units
# github.com/dustin/go-humanize v1.0.1
## explicit; go 1.16
github.com/dustin/go-humanize
# github.com/emicklei/go-restful/v3 v3.12.2
## explicit; go 1.13
github.com/emicklei/go-restful/v3
github.com/emicklei/go-restful/v3/log
# github.com/euank/go-kmsg-parser v2.0.0+incompatible
## explicit
github.com/euank/go-kmsg-parser/kmsgparser
# github.com/exponent-io/jsonpath v0.0.0-20210407135951-1de76d718b3f
## explicit; go 1.15
github.com/exponent-io/jsonpath
# github.com/fatih/camelcase v1.0.0
## explicit
github.com/fatih/camelcase
# github.com/felixge/httpsnoop v1.0.4
## explicit; go 1.13
github.com/felixge/httpsnoop
# github.com/fsnotify/fsnotify v1.9.0
## explicit; go 1.17
github.com/fsnotify/fsnotify
github.com/fsnotify/fsnotify/internal
# github.com/fxamacker/cbor/v2 v2.9.0
## explicit; go 1.20
github.com/fxamacker/cbor/v2
# github.com/go-errors/errors v1.4.2
## explicit; go 1.14
github.com/go-errors/errors
# github.com/go-logr/logr v1.4.2
## explicit; go 1.18
github.com/go-logr/logr
github.com/go-logr/logr/funcr
github.com/go-logr/logr/slogr
# github.com/go-logr/stdr v1.2.2
## explicit; go 1.16
github.com/go-logr/stdr
# github.com/go-logr/zapr v1.3.0
## explicit; go 1.18
github.com/go-logr/zapr
# github.com/go-openapi/jsonpointer v0.21.0
## explicit; go 1.20
github.com/go-openapi/jsonpointer
# github.com/go-openapi/jsonreference v0.20.2
## explicit; go 1.13
github.com/go-openapi/jsonreference
github.com/go-openapi/jsonreference/internal
# github.com/go-openapi/swag v0.23.0
## explicit; go 1.20
github.com/go-openapi/swag
# github.com/go-task/slim-sprig/v3 v3.0.0
## explicit; go 1.20
github.com/go-task/slim-sprig/v3
# github.com/godbus/dbus/v5 v5.1.0
## explicit; go 1.12
github.com/godbus/dbus/v5
# github.com/gogo/protobuf v1.3.2
## explicit; go 1.15
github.com/gogo/protobuf/gogoproto
github.com/gogo/protobuf/plugin/compare
github.com/gogo/protobuf/plugin/defaultcheck
github.com/gogo/protobuf/plugin/description
github.com/gogo/protobuf/plugin/embedcheck
github.com/gogo/protobuf/plugin/enumstringer
github.com/gogo/protobuf/plugin/equal
github.com/gogo/protobuf/plugin/face
github.com/gogo/protobuf/plugin/gostring
github.com/gogo/protobuf/plugin/marshalto
github.com/gogo/protobuf/plugin/oneofcheck
github.com/gogo/protobuf/plugin/populate
github.com/gogo/protobuf/plugin/size
github.com/gogo/protobuf/plugin/stringer
github.com/gogo/protobuf/plugin/testgen
github.com/gogo/protobuf/plugin/union
github.com/gogo/protobuf/plugin/unmarshal
github.com/gogo/protobuf/proto
github.com/gogo/protobuf/protoc-gen-gogo/descriptor
github.com/gogo/protobuf/protoc-gen-gogo/generator
github.com/gogo/protobuf/protoc-gen-gogo/generator/internal/remap
github.com/gogo/protobuf/protoc-gen-gogo/grpc
github.com/gogo/protobuf/protoc-gen-gogo/plugin
github.com/gogo/protobuf/sortkeys
github.com/gogo/protobuf/vanity
github.com/gogo/protobuf/vanity/command
# github.com/golang-jwt/jwt/v5 v5.2.2
## explicit; go 1.18
github.com/golang-jwt/jwt/v5
# github.com/golang/protobuf v1.5.4
## explicit; go 1.17
github.com/golang/protobuf/proto
github.com/golang/protobuf/protoc-gen-go/descriptor
github.com/golang/protobuf/ptypes/timestamp
github.com/golang/protobuf/ptypes/wrappers
# github.com/google/btree v1.1.3
## explicit; go 1.18
github.com/google/btree
# github.com/google/cadvisor v0.52.1
## explicit; go 1.23.0
github.com/google/cadvisor/cache/memory
github.com/google/cadvisor/client/v2
github.com/google/cadvisor/collector
github.com/google/cadvisor/container
github.com/google/cadvisor/container/common
github.com/google/cadvisor/container/containerd
github.com/google/cadvisor/container/containerd/containers
github.com/google/cadvisor/container/containerd/identifiers
github.com/google/cadvisor/container/containerd/install
github.com/google/cadvisor/container/containerd/namespaces
github.com/google/cadvisor/container/containerd/pkg/dialer
github.com/google/cadvisor/container/crio
github.com/google/cadvisor/container/crio/install
github.com/google/cadvisor/container/libcontainer
github.com/google/cadvisor/container/raw
github.com/google/cadvisor/container/systemd
github.com/google/cadvisor/container/systemd/install
github.com/google/cadvisor/devicemapper
github.com/google/cadvisor/events
github.com/google/cadvisor/fs
github.com/google/cadvisor/info/v1
github.com/google/cadvisor/info/v2
github.com/google/cadvisor/machine
github.com/google/cadvisor/manager
github.com/google/cadvisor/metrics
github.com/google/cadvisor/nvm
github.com/google/cadvisor/perf
github.com/google/cadvisor/resctrl
github.com/google/cadvisor/stats
github.com/google/cadvisor/storage
github.com/google/cadvisor/summary
github.com/google/cadvisor/utils
github.com/google/cadvisor/utils/cloudinfo
github.com/google/cadvisor/utils/cpuload
github.com/google/cadvisor/utils/cpuload/netlink
github.com/google/cadvisor/utils/oomparser
github.com/google/cadvisor/utils/sysfs
github.com/google/cadvisor/utils/sysinfo
github.com/google/cadvisor/version
github.com/google/cadvisor/watcher
# github.com/google/cel-go v0.26.0
## explicit; go 1.22.0
github.com/google/cel-go/cel
github.com/google/cel-go/checker
github.com/google/cel-go/checker/decls
github.com/google/cel-go/common
github.com/google/cel-go/common/ast
github.com/google/cel-go/common/containers
github.com/google/cel-go/common/debug
github.com/google/cel-go/common/decls
github.com/google/cel-go/common/env
github.com/google/cel-go/common/functions
github.com/google/cel-go/common/operators
github.com/google/cel-go/common/overloads
github.com/google/cel-go/common/runes
github.com/google/cel-go/common/stdlib
github.com/google/cel-go/common/types
github.com/google/cel-go/common/types/pb
github.com/google/cel-go/common/types/ref
github.com/google/cel-go/common/types/traits
github.com/google/cel-go/ext
github.com/google/cel-go/interpreter
github.com/google/cel-go/interpreter/functions
github.com/google/cel-go/parser
github.com/google/cel-go/parser/gen
# github.com/google/gnostic-models v0.7.0
## explicit; go 1.22
github.com/google/gnostic-models/compiler
github.com/google/gnostic-models/extensions
github.com/google/gnostic-models/jsonschema
github.com/google/gnostic-models/openapiv2
github.com/google/gnostic-models/openapiv3
# github.com/google/go-cmp v0.7.0
## explicit; go 1.21
github.com/google/go-cmp/cmp
github.com/google/go-cmp/cmp/cmpopts
github.com/google/go-cmp/cmp/internal/diff
github.com/google/go-cmp/cmp/internal/flags
github.com/google/go-cmp/cmp/internal/function
github.com/google/go-cmp/cmp/internal/value
# github.com/google/pprof v0.0.0-20241029153458-d1b30febd7db
## explicit; go 1.22
github.com/google/pprof/profile
# github.com/google/uuid v1.6.0
## explicit
github.com/google/uuid
# github.com/gorilla/websocket v1.5.4-0.20250319132907-e064f32e3674
## explicit; go 1.20
github.com/gorilla/websocket
# github.com/gregjones/httpcache v0.0.0-20190611155906-901d90724c79
## explicit
github.com/gregjones/httpcache
# github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus v1.0.1
## explicit; go 1.19
github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus
# github.com/grpc-ecosystem/go-grpc-middleware/v2 v2.3.0
## explicit; go 1.23
github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors
# github.com/grpc-ecosystem/go-grpc-prometheus v1.2.0
## explicit
github.com/grpc-ecosystem/go-grpc-prometheus
# github.com/grpc-ecosystem/grpc-gateway/v2 v2.26.3
## explicit; go 1.23.0
github.com/grpc-ecosystem/grpc-gateway/v2/internal/httprule
github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options
github.com/grpc-ecosystem/grpc-gateway/v2/runtime
github.com/grpc-ecosystem/grpc-gateway/v2/utilities
# github.com/inconshreveable/mousetrap v1.1.0
## explicit; go 1.18
github.com/inconshreveable/mousetrap
# github.com/ishidawataru/sctp v0.0.0-20250521072954-ae8eb7fa7995
## explicit; go 1.12
github.com/ishidawataru/sctp
# github.com/jonboulle/clockwork v0.5.0
## explicit; go 1.21
github.com/jonboulle/clockwork
# github.com/josharian/intern v1.0.0
## explicit; go 1.5
github.com/josharian/intern
# github.com/json-iterator/go v1.1.12
## explicit; go 1.12
github.com/json-iterator/go
# github.com/karrick/godirwalk v1.17.0
## explicit; go 1.13
github.com/karrick/godirwalk
# github.com/kr/pretty v0.3.1
## explicit; go 1.12
# github.com/kr/text v0.2.0
## explicit
# github.com/kylelemons/godebug v1.1.0
## explicit; go 1.11
github.com/kylelemons/godebug/diff
# github.com/libopenstorage/openstorage v1.0.0
## explicit
github.com/libopenstorage/openstorage/api
github.com/libopenstorage/openstorage/api/client
github.com/libopenstorage/openstorage/api/client/volume
github.com/libopenstorage/openstorage/api/spec
github.com/libopenstorage/openstorage/pkg/parser
github.com/libopenstorage/openstorage/pkg/units
github.com/libopenstorage/openstorage/volume
# github.com/liggitt/tabwriter v0.0.0-20181228230101-89fcab3d43de
## explicit
github.com/liggitt/tabwriter
# github.com/lithammer/dedent v1.1.0
## explicit
github.com/lithammer/dedent
# github.com/mailru/easyjson v0.7.7
## explicit; go 1.12
github.com/mailru/easyjson/buffer
github.com/mailru/easyjson/jlexer
github.com/mailru/easyjson/jwriter
# github.com/mistifyio/go-zfs v2.1.2-0.20190413222219-f784269be439+incompatible
## explicit
github.com/mistifyio/go-zfs
# github.com/mitchellh/go-wordwrap v1.0.1
## explicit; go 1.14
github.com/mitchellh/go-wordwrap
# github.com/moby/ipvs v1.1.0
## explicit; go 1.17
github.com/moby/ipvs
# github.com/moby/spdystream v0.5.0
## explicit; go 1.13
github.com/moby/spdystream
github.com/moby/spdystream/spdy
# github.com/moby/sys/mountinfo v0.7.2
## explicit; go 1.17
github.com/moby/sys/mountinfo
# github.com/moby/sys/userns v0.1.0
## explicit; go 1.21
github.com/moby/sys/userns
# github.com/moby/term v0.5.0
## explicit; go 1.18
github.com/moby/term
github.com/moby/term/windows
# github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd
## explicit
github.com/modern-go/concurrent
# github.com/modern-go/reflect2 v1.0.3-0.20250322232337-35a7c28c31ee
## explicit; go 1.12
github.com/modern-go/reflect2
# github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826
## explicit
github.com/mohae/deepcopy
# github.com/monochromegane/go-gitignore v0.0.0-20200626010858-205db1a8cc00
## explicit
github.com/monochromegane/go-gitignore
# github.com/mrunalp/fileutils v0.5.1
## explicit; go 1.13
github.com/mrunalp/fileutils
# github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822
## explicit
github.com/munnerz/goautoneg
# github.com/mxk/go-flowrate v0.0.0-20140419014527-cca7078d478f
## explicit
github.com/mxk/go-flowrate/flowrate
# github.com/onsi/ginkgo/v2 v2.21.0
## explicit; go 1.22.0
github.com/onsi/ginkgo/v2
github.com/onsi/ginkgo/v2/config
github.com/onsi/ginkgo/v2/formatter
github.com/onsi/ginkgo/v2/ginkgo
github.com/onsi/ginkgo/v2/ginkgo/build
github.com/onsi/ginkgo/v2/ginkgo/command
github.com/onsi/ginkgo/v2/ginkgo/generators
github.com/onsi/ginkgo/v2/ginkgo/internal
github.com/onsi/ginkgo/v2/ginkgo/labels
github.com/onsi/ginkgo/v2/ginkgo/outline
github.com/onsi/ginkgo/v2/ginkgo/run
github.com/onsi/ginkgo/v2/ginkgo/unfocus
github.com/onsi/ginkgo/v2/ginkgo/watch
github.com/onsi/ginkgo/v2/internal
github.com/onsi/ginkgo/v2/internal/global
github.com/onsi/ginkgo/v2/internal/interrupt_handler
github.com/onsi/ginkgo/v2/internal/parallel_support
github.com/onsi/ginkgo/v2/internal/testingtproxy
github.com/onsi/ginkgo/v2/reporters
github.com/onsi/ginkgo/v2/types
# github.com/onsi/gomega v1.35.1
## explicit; go 1.22
github.com/onsi/gomega
github.com/onsi/gomega/format
github.com/onsi/gomega/gcustom
github.com/onsi/gomega/gmeasure
github.com/onsi/gomega/gmeasure/table
github.com/onsi/gomega/gstruct
github.com/onsi/gomega/gstruct/errors
github.com/onsi/gomega/internal
github.com/onsi/gomega/internal/gutil
github.com/onsi/gomega/matchers
github.com/onsi/gomega/matchers/support/goraph/bipartitegraph
github.com/onsi/gomega/matchers/support/goraph/edge
github.com/onsi/gomega/matchers/support/goraph/node
github.com/onsi/gomega/matchers/support/goraph/util
github.com/onsi/gomega/types
# github.com/opencontainers/cgroups v0.0.1
## explicit; go 1.23.0
github.com/opencontainers/cgroups
github.com/opencontainers/cgroups/devices/config
github.com/opencontainers/cgroups/fs
github.com/opencontainers/cgroups/fs2
github.com/opencontainers/cgroups/fscommon
github.com/opencontainers/cgroups/internal/path
github.com/opencontainers/cgroups/manager
github.com/opencontainers/cgroups/systemd
# github.com/opencontainers/go-digest v1.0.0
## explicit; go 1.13
github.com/opencontainers/go-digest
# github.com/opencontainers/image-spec v1.1.1
## explicit; go 1.18
github.com/opencontainers/image-spec/specs-go
github.com/opencontainers/image-spec/specs-go/v1
# github.com/opencontainers/runtime-spec v1.2.0
## explicit
github.com/opencontainers/runtime-spec/specs-go
# github.com/opencontainers/selinux v1.11.1
## explicit; go 1.19
github.com/opencontainers/selinux/go-selinux
github.com/opencontainers/selinux/go-selinux/label
github.com/opencontainers/selinux/pkg/pwalkdir
# github.com/peterbourgon/diskv v2.0.1+incompatible
## explicit
github.com/peterbourgon/diskv
# github.com/pkg/errors v0.9.1
## explicit
github.com/pkg/errors
# github.com/pmezard/go-difflib v1.0.0
## explicit
github.com/pmezard/go-difflib/difflib
# github.com/pquerna/cachecontrol v0.1.0
## explicit; go 1.16
github.com/pquerna/cachecontrol
github.com/pquerna/cachecontrol/cacheobject
# github.com/prometheus/client_golang v1.22.0
## explicit; go 1.22
github.com/prometheus/client_golang/internal/github.com/golang/gddo/httputil
github.com/prometheus/client_golang/internal/github.com/golang/gddo/httputil/header
github.com/prometheus/client_golang/prometheus
github.com/prometheus/client_golang/prometheus/collectors
github.com/prometheus/client_golang/prometheus/internal
github.com/prometheus/client_golang/prometheus/promhttp
github.com/prometheus/client_golang/prometheus/promhttp/internal
github.com/prometheus/client_golang/prometheus/testutil
github.com/prometheus/client_golang/prometheus/testutil/promlint
github.com/prometheus/client_golang/prometheus/testutil/promlint/validations
# github.com/prometheus/client_model v0.6.1
## explicit; go 1.19
github.com/prometheus/client_model/go
# github.com/prometheus/common v0.62.0
## explicit; go 1.21
github.com/prometheus/common/expfmt
github.com/prometheus/common/model
# github.com/prometheus/procfs v0.15.1
## explicit; go 1.20
github.com/prometheus/procfs
github.com/prometheus/procfs/internal/fs
github.com/prometheus/procfs/internal/util
# github.com/robfig/cron/v3 v3.0.1
## explicit; go 1.12
github.com/robfig/cron/v3
# github.com/rogpeppe/go-internal v1.13.1
## explicit; go 1.22
# github.com/russross/blackfriday/v2 v2.1.0
## explicit
github.com/russross/blackfriday/v2
# github.com/sirupsen/logrus v1.9.3
## explicit; go 1.13
github.com/sirupsen/logrus
# github.com/soheilhy/cmux v0.1.5
## explicit; go 1.11
github.com/soheilhy/cmux
# github.com/spf13/cobra v1.9.1
## explicit; go 1.15
github.com/spf13/cobra
github.com/spf13/cobra/doc
# github.com/spf13/pflag v1.0.6
## explicit; go 1.12
github.com/spf13/pflag
# github.com/stoewer/go-strcase v1.3.0
## explicit; go 1.11
github.com/stoewer/go-strcase
# github.com/stretchr/objx v0.5.2
## explicit; go 1.20
github.com/stretchr/objx
# github.com/stretchr/testify v1.10.0
## explicit; go 1.17
github.com/stretchr/testify/assert
github.com/stretchr/testify/assert/yaml
github.com/stretchr/testify/mock
github.com/stretchr/testify/require
# github.com/tmc/grpc-websocket-proxy v0.0.0-20220101234140-673ab2c3ae75
## explicit; go 1.15
github.com/tmc/grpc-websocket-proxy/wsproxy
# github.com/vishvananda/netlink v1.3.1
## explicit; go 1.12
github.com/vishvananda/netlink
github.com/vishvananda/netlink/nl
# github.com/vishvananda/netns v0.0.5
## explicit; go 1.17
github.com/vishvananda/netns
# github.com/x448/float16 v0.8.4
## explicit; go 1.11
github.com/x448/float16
# github.com/xiang90/probing v0.0.0-20221125231312-a49e3df8f510
## explicit
github.com/xiang90/probing
# github.com/xlab/treeprint v1.2.0
## explicit; go 1.13
github.com/xlab/treeprint
# go.etcd.io/bbolt v1.4.0
## explicit; go 1.23
go.etcd.io/bbolt
go.etcd.io/bbolt/errors
go.etcd.io/bbolt/internal/common
go.etcd.io/bbolt/internal/freelist
# go.etcd.io/etcd/api/v3 v3.6.1
## explicit; go 1.23.0
go.etcd.io/etcd/api/v3/authpb
go.etcd.io/etcd/api/v3/etcdserverpb
go.etcd.io/etcd/api/v3/etcdserverpb/gw
go.etcd.io/etcd/api/v3/membershippb
go.etcd.io/etcd/api/v3/mvccpb
go.etcd.io/etcd/api/v3/v3rpc/rpctypes
go.etcd.io/etcd/api/v3/version
go.etcd.io/etcd/api/v3/versionpb
# go.etcd.io/etcd/client/pkg/v3 v3.6.1
## explicit; go 1.23.0
go.etcd.io/etcd/client/pkg/v3/fileutil
go.etcd.io/etcd/client/pkg/v3/logutil
go.etcd.io/etcd/client/pkg/v3/pathutil
go.etcd.io/etcd/client/pkg/v3/srv
go.etcd.io/etcd/client/pkg/v3/systemd
go.etcd.io/etcd/client/pkg/v3/tlsutil
go.etcd.io/etcd/client/pkg/v3/transport
go.etcd.io/etcd/client/pkg/v3/types
go.etcd.io/etcd/client/pkg/v3/verify
# go.etcd.io/etcd/client/v3 v3.6.1
## explicit; go 1.23.0
go.etcd.io/etcd/client/v3
go.etcd.io/etcd/client/v3/concurrency
go.etcd.io/etcd/client/v3/credentials
go.etcd.io/etcd/client/v3/internal/endpoint
go.etcd.io/etcd/client/v3/internal/resolver
go.etcd.io/etcd/client/v3/kubernetes
# go.etcd.io/etcd/pkg/v3 v3.6.1
## explicit; go 1.23.0
go.etcd.io/etcd/pkg/v3/adt
go.etcd.io/etcd/pkg/v3/contention
go.etcd.io/etcd/pkg/v3/cpuutil
go.etcd.io/etcd/pkg/v3/crc
go.etcd.io/etcd/pkg/v3/debugutil
go.etcd.io/etcd/pkg/v3/featuregate
go.etcd.io/etcd/pkg/v3/flags
go.etcd.io/etcd/pkg/v3/httputil
go.etcd.io/etcd/pkg/v3/idutil
go.etcd.io/etcd/pkg/v3/ioutil
go.etcd.io/etcd/pkg/v3/netutil
go.etcd.io/etcd/pkg/v3/notify
go.etcd.io/etcd/pkg/v3/pbutil
go.etcd.io/etcd/pkg/v3/runtime
go.etcd.io/etcd/pkg/v3/schedule
go.etcd.io/etcd/pkg/v3/traceutil
go.etcd.io/etcd/pkg/v3/wait
# go.etcd.io/etcd/server/v3 v3.6.1
## explicit; go 1.23.0
go.etcd.io/etcd/server/v3/auth
go.etcd.io/etcd/server/v3/config
go.etcd.io/etcd/server/v3/embed
go.etcd.io/etcd/server/v3/etcdserver
go.etcd.io/etcd/server/v3/etcdserver/api
go.etcd.io/etcd/server/v3/etcdserver/api/etcdhttp
go.etcd.io/etcd/server/v3/etcdserver/api/etcdhttp/types
go.etcd.io/etcd/server/v3/etcdserver/api/membership
go.etcd.io/etcd/server/v3/etcdserver/api/rafthttp
go.etcd.io/etcd/server/v3/etcdserver/api/snap
go.etcd.io/etcd/server/v3/etcdserver/api/snap/snappb
go.etcd.io/etcd/server/v3/etcdserver/api/v2discovery
go.etcd.io/etcd/server/v3/etcdserver/api/v2error
go.etcd.io/etcd/server/v3/etcdserver/api/v2stats
go.etcd.io/etcd/server/v3/etcdserver/api/v2store
go.etcd.io/etcd/server/v3/etcdserver/api/v3alarm
go.etcd.io/etcd/server/v3/etcdserver/api/v3client
go.etcd.io/etcd/server/v3/etcdserver/api/v3compactor
go.etcd.io/etcd/server/v3/etcdserver/api/v3discovery
go.etcd.io/etcd/server/v3/etcdserver/api/v3election
go.etcd.io/etcd/server/v3/etcdserver/api/v3election/v3electionpb
go.etcd.io/etcd/server/v3/etcdserver/api/v3election/v3electionpb/gw
go.etcd.io/etcd/server/v3/etcdserver/api/v3lock
go.etcd.io/etcd/server/v3/etcdserver/api/v3lock/v3lockpb
go.etcd.io/etcd/server/v3/etcdserver/api/v3lock/v3lockpb/gw
go.etcd.io/etcd/server/v3/etcdserver/api/v3rpc
go.etcd.io/etcd/server/v3/etcdserver/apply
go.etcd.io/etcd/server/v3/etcdserver/cindex
go.etcd.io/etcd/server/v3/etcdserver/errors
go.etcd.io/etcd/server/v3/etcdserver/txn
go.etcd.io/etcd/server/v3/etcdserver/version
go.etcd.io/etcd/server/v3/features
go.etcd.io/etcd/server/v3/internal/clientv2
go.etcd.io/etcd/server/v3/lease
go.etcd.io/etcd/server/v3/lease/leasehttp
go.etcd.io/etcd/server/v3/lease/leasepb
go.etcd.io/etcd/server/v3/proxy/grpcproxy/adapter
go.etcd.io/etcd/server/v3/storage
go.etcd.io/etcd/server/v3/storage/backend
go.etcd.io/etcd/server/v3/storage/datadir
go.etcd.io/etcd/server/v3/storage/mvcc
go.etcd.io/etcd/server/v3/storage/schema
go.etcd.io/etcd/server/v3/storage/wal
go.etcd.io/etcd/server/v3/storage/wal/walpb
go.etcd.io/etcd/server/v3/verify
# go.etcd.io/raft/v3 v3.6.0
## explicit; go 1.23
go.etcd.io/raft/v3
go.etcd.io/raft/v3/confchange
go.etcd.io/raft/v3/quorum
go.etcd.io/raft/v3/raftpb
go.etcd.io/raft/v3/tracker
# go.opentelemetry.io/auto/sdk v1.1.0
## explicit; go 1.22.0
go.opentelemetry.io/auto/sdk
go.opentelemetry.io/auto/sdk/internal/telemetry
# go.opentelemetry.io/contrib/instrumentation/github.com/emicklei/go-restful/otelrestful v0.44.0
## explicit; go 1.19
go.opentelemetry.io/contrib/instrumentation/github.com/emicklei/go-restful/otelrestful
go.opentelemetry.io/contrib/instrumentation/github.com/emicklei/go-restful/otelrestful/internal/semconvutil
# go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc v0.60.0
## explicit; go 1.22.0
go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc
go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc/internal
# go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.58.0
## explicit; go 1.22.0
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp/internal/request
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp/internal/semconv
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp/internal/semconvutil
# go.opentelemetry.io/otel v1.35.0
## explicit; go 1.22.0
go.opentelemetry.io/otel
go.opentelemetry.io/otel/attribute
go.opentelemetry.io/otel/baggage
go.opentelemetry.io/otel/codes
go.opentelemetry.io/otel/internal
go.opentelemetry.io/otel/internal/attribute
go.opentelemetry.io/otel/internal/baggage
go.opentelemetry.io/otel/internal/global
go.opentelemetry.io/otel/propagation
go.opentelemetry.io/otel/semconv/internal
go.opentelemetry.io/otel/semconv/v1.12.0
go.opentelemetry.io/otel/semconv/v1.17.0
go.opentelemetry.io/otel/semconv/v1.20.0
go.opentelemetry.io/otel/semconv/v1.26.0
# go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.34.0
## explicit; go 1.22.0
go.opentelemetry.io/otel/exporters/otlp/otlptrace
go.opentelemetry.io/otel/exporters/otlp/otlptrace/internal/tracetransform
# go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.34.0
## explicit; go 1.22.0
go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc
go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc/internal
go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc/internal/envconfig
go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc/internal/otlpconfig
go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc/internal/retry
# go.opentelemetry.io/otel/metric v1.35.0
## explicit; go 1.22.0
go.opentelemetry.io/otel/metric
go.opentelemetry.io/otel/metric/embedded
go.opentelemetry.io/otel/metric/noop
# go.opentelemetry.io/otel/sdk v1.34.0
## explicit; go 1.22.0
go.opentelemetry.io/otel/sdk
go.opentelemetry.io/otel/sdk/instrumentation
go.opentelemetry.io/otel/sdk/internal/env
go.opentelemetry.io/otel/sdk/internal/x
go.opentelemetry.io/otel/sdk/resource
go.opentelemetry.io/otel/sdk/trace
go.opentelemetry.io/otel/sdk/trace/tracetest
# go.opentelemetry.io/otel/trace v1.35.0
## explicit; go 1.22.0
go.opentelemetry.io/otel/trace
go.opentelemetry.io/otel/trace/embedded
go.opentelemetry.io/otel/trace/internal/telemetry
go.opentelemetry.io/otel/trace/noop
# go.opentelemetry.io/proto/otlp v1.5.0
## explicit; go 1.22.0
go.opentelemetry.io/proto/otlp/collector/trace/v1
go.opentelemetry.io/proto/otlp/common/v1
go.opentelemetry.io/proto/otlp/resource/v1
go.opentelemetry.io/proto/otlp/trace/v1
# go.uber.org/goleak v1.3.0
## explicit; go 1.20
go.uber.org/goleak
go.uber.org/goleak/internal/stack
# go.uber.org/multierr v1.11.0
## explicit; go 1.19
go.uber.org/multierr
# go.uber.org/zap v1.27.0
## explicit; go 1.19
go.uber.org/zap
go.uber.org/zap/buffer
go.uber.org/zap/internal
go.uber.org/zap/internal/bufferpool
go.uber.org/zap/internal/color
go.uber.org/zap/internal/exit
go.uber.org/zap/internal/pool
go.uber.org/zap/internal/stacktrace
go.uber.org/zap/internal/ztest
go.uber.org/zap/zapcore
go.uber.org/zap/zapgrpc
go.uber.org/zap/zaptest
# go.yaml.in/yaml/v2 v2.4.2
## explicit; go 1.15
go.yaml.in/yaml/v2
# go.yaml.in/yaml/v3 v3.0.4
## explicit; go 1.16
go.yaml.in/yaml/v3
# golang.org/x/crypto v0.36.0
## explicit; go 1.23.0
golang.org/x/crypto/bcrypt
golang.org/x/crypto/blowfish
golang.org/x/crypto/chacha20
golang.org/x/crypto/cryptobyte
golang.org/x/crypto/cryptobyte/asn1
golang.org/x/crypto/curve25519
golang.org/x/crypto/ed25519
golang.org/x/crypto/hkdf
golang.org/x/crypto/internal/alias
golang.org/x/crypto/internal/poly1305
golang.org/x/crypto/nacl/secretbox
golang.org/x/crypto/pbkdf2
golang.org/x/crypto/salsa20/salsa
golang.org/x/crypto/ssh
golang.org/x/crypto/ssh/internal/bcrypt_pbkdf
# golang.org/x/exp v0.0.0-20240719175910-8a7402abbf56
## explicit; go 1.20
golang.org/x/exp/constraints
golang.org/x/exp/slices
# golang.org/x/mod v0.21.0
## explicit; go 1.22.0
golang.org/x/mod/internal/lazyregexp
golang.org/x/mod/module
golang.org/x/mod/semver
# golang.org/x/net v0.38.0
## explicit; go 1.23.0
golang.org/x/net/context
golang.org/x/net/html
golang.org/x/net/html/atom
golang.org/x/net/html/charset
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/hpack
golang.org/x/net/idna
golang.org/x/net/internal/httpcommon
golang.org/x/net/internal/socks
golang.org/x/net/internal/timeseries
golang.org/x/net/proxy
golang.org/x/net/trace
golang.org/x/net/websocket
# golang.org/x/oauth2 v0.27.0
## explicit; go 1.23.0
golang.org/x/oauth2
golang.org/x/oauth2/internal
# golang.org/x/sync v0.12.0
## explicit; go 1.23.0
golang.org/x/sync/errgroup
golang.org/x/sync/singleflight
# golang.org/x/sys v0.31.0
## explicit; go 1.23.0
golang.org/x/sys/cpu
golang.org/x/sys/plan9
golang.org/x/sys/unix
golang.org/x/sys/windows
golang.org/x/sys/windows/registry
golang.org/x/sys/windows/svc
golang.org/x/sys/windows/svc/mgr
# golang.org/x/term v0.30.0
## explicit; go 1.23.0
golang.org/x/term
# golang.org/x/text v0.23.0
## explicit; go 1.23.0
golang.org/x/text/cases
golang.org/x/text/encoding
golang.org/x/text/encoding/charmap
golang.org/x/text/encoding/htmlindex
golang.org/x/text/encoding/internal
golang.org/x/text/encoding/internal/identifier
golang.org/x/text/encoding/japanese
golang.org/x/text/encoding/korean
golang.org/x/text/encoding/simplifiedchinese
golang.org/x/text/encoding/traditionalchinese
golang.org/x/text/encoding/unicode
golang.org/x/text/feature/plural
golang.org/x/text/internal
golang.org/x/text/internal/catmsg
golang.org/x/text/internal/format
golang.org/x/text/internal/language
golang.org/x/text/internal/language/compact
golang.org/x/text/internal/number
golang.org/x/text/internal/stringset
golang.org/x/text/internal/tag
golang.org/x/text/internal/utf8internal
golang.org/x/text/language
golang.org/x/text/message
golang.org/x/text/message/catalog
golang.org/x/text/runes
golang.org/x/text/secure/bidirule
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
# golang.org/x/time v0.9.0
## explicit; go 1.18
golang.org/x/time/rate
# golang.org/x/tools v0.26.0
## explicit; go 1.22.0
golang.org/x/tools/benchmark/parse
golang.org/x/tools/container/intsets
golang.org/x/tools/cover
golang.org/x/tools/go/ast/astutil
golang.org/x/tools/go/ast/inspector
golang.org/x/tools/go/gcexportdata
golang.org/x/tools/go/packages
golang.org/x/tools/go/types/objectpath
golang.org/x/tools/go/types/typeutil
golang.org/x/tools/imports
golang.org/x/tools/internal/aliases
golang.org/x/tools/internal/event
golang.org/x/tools/internal/event/core
golang.org/x/tools/internal/event/keys
golang.org/x/tools/internal/event/label
golang.org/x/tools/internal/gcimporter
golang.org/x/tools/internal/gocommand
golang.org/x/tools/internal/gopathwalk
golang.org/x/tools/internal/imports
golang.org/x/tools/internal/packagesinternal
golang.org/x/tools/internal/pkgbits
golang.org/x/tools/internal/stdlib
golang.org/x/tools/internal/typeparams
golang.org/x/tools/internal/typesinternal
golang.org/x/tools/internal/versions
# google.golang.org/genproto/googleapis/api v0.0.0-20250303144028-a0af3efb3deb
## explicit; go 1.23.0
google.golang.org/genproto/googleapis/api
google.golang.org/genproto/googleapis/api/annotations
google.golang.org/genproto/googleapis/api/expr/v1alpha1
google.golang.org/genproto/googleapis/api/httpbody
# google.golang.org/genproto/googleapis/rpc v0.0.0-20250303144028-a0af3efb3deb
## explicit; go 1.23.0
google.golang.org/genproto/googleapis/rpc/errdetails
google.golang.org/genproto/googleapis/rpc/status
# google.golang.org/grpc v1.72.1
## explicit; go 1.23
google.golang.org/grpc
google.golang.org/grpc/attributes
google.golang.org/grpc/backoff
google.golang.org/grpc/balancer
google.golang.org/grpc/balancer/base
google.golang.org/grpc/balancer/endpointsharding
google.golang.org/grpc/balancer/grpclb/state
google.golang.org/grpc/balancer/pickfirst
google.golang.org/grpc/balancer/pickfirst/internal
google.golang.org/grpc/balancer/pickfirst/pickfirstleaf
google.golang.org/grpc/balancer/roundrobin
google.golang.org/grpc/binarylog/grpc_binarylog_v1
google.golang.org/grpc/channelz
google.golang.org/grpc/codes
google.golang.org/grpc/connectivity
google.golang.org/grpc/credentials
google.golang.org/grpc/credentials/insecure
google.golang.org/grpc/encoding
google.golang.org/grpc/encoding/gzip
google.golang.org/grpc/encoding/proto
google.golang.org/grpc/experimental/stats
google.golang.org/grpc/grpclog
google.golang.org/grpc/grpclog/internal
google.golang.org/grpc/health
google.golang.org/grpc/health/grpc_health_v1
google.golang.org/grpc/internal
google.golang.org/grpc/internal/backoff
google.golang.org/grpc/internal/balancer/gracefulswitch
google.golang.org/grpc/internal/balancerload
google.golang.org/grpc/internal/binarylog
google.golang.org/grpc/internal/buffer
google.golang.org/grpc/internal/channelz
google.golang.org/grpc/internal/credentials
google.golang.org/grpc/internal/envconfig
google.golang.org/grpc/internal/grpclog
google.golang.org/grpc/internal/grpcsync
google.golang.org/grpc/internal/grpcutil
google.golang.org/grpc/internal/idle
google.golang.org/grpc/internal/metadata
google.golang.org/grpc/internal/pretty
google.golang.org/grpc/internal/proxyattributes
google.golang.org/grpc/internal/resolver
google.golang.org/grpc/internal/resolver/delegatingresolver
google.golang.org/grpc/internal/resolver/dns
google.golang.org/grpc/internal/resolver/dns/internal
google.golang.org/grpc/internal/resolver/passthrough
google.golang.org/grpc/internal/resolver/unix
google.golang.org/grpc/internal/serviceconfig
google.golang.org/grpc/internal/stats
google.golang.org/grpc/internal/status
google.golang.org/grpc/internal/syscall
google.golang.org/grpc/internal/transport
google.golang.org/grpc/internal/transport/networktype
google.golang.org/grpc/keepalive
google.golang.org/grpc/mem
google.golang.org/grpc/metadata
google.golang.org/grpc/peer
google.golang.org/grpc/reflection
google.golang.org/grpc/reflection/grpc_reflection_v1
google.golang.org/grpc/reflection/grpc_reflection_v1alpha
google.golang.org/grpc/reflection/internal
google.golang.org/grpc/resolver
google.golang.org/grpc/resolver/dns
google.golang.org/grpc/resolver/manual
google.golang.org/grpc/serviceconfig
google.golang.org/grpc/stats
google.golang.org/grpc/status
google.golang.org/grpc/tap
# google.golang.org/protobuf v1.36.5
## explicit; go 1.21
google.golang.org/protobuf/encoding/protodelim
google.golang.org/protobuf/encoding/protojson
google.golang.org/protobuf/encoding/prototext
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/descfmt
google.golang.org/protobuf/internal/descopts
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/editiondefaults
google.golang.org/protobuf/internal/editionssupport
google.golang.org/protobuf/internal/encoding/defval
google.golang.org/protobuf/internal/encoding/json
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/encoding/tag
google.golang.org/protobuf/internal/encoding/text
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/filedesc
google.golang.org/protobuf/internal/filetype
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/impl
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/protolazy
google.golang.org/protobuf/internal/set
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/internal/version
google.golang.org/protobuf/proto
google.golang.org/protobuf/protoadapt
google.golang.org/protobuf/reflect/protodesc
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
google.golang.org/protobuf/runtime/protoimpl
google.golang.org/protobuf/types/descriptorpb
google.golang.org/protobuf/types/dynamicpb
google.golang.org/protobuf/types/gofeaturespb
google.golang.org/protobuf/types/known/anypb
google.golang.org/protobuf/types/known/durationpb
google.golang.org/protobuf/types/known/emptypb
google.golang.org/protobuf/types/known/fieldmaskpb
google.golang.org/protobuf/types/known/structpb
google.golang.org/protobuf/types/known/timestamppb
google.golang.org/protobuf/types/known/wrapperspb
# gopkg.in/check.v1 v1.0.0-20201130134442-10cb98267c6c
## explicit; go 1.11
# gopkg.in/evanphx/json-patch.v4 v4.12.0
## explicit
gopkg.in/evanphx/json-patch.v4
# gopkg.in/go-jose/go-jose.v2 v2.6.3
## explicit
gopkg.in/go-jose/go-jose.v2
gopkg.in/go-jose/go-jose.v2/cipher
gopkg.in/go-jose/go-jose.v2/json
gopkg.in/go-jose/go-jose.v2/jwt
# gopkg.in/inf.v0 v0.9.1
## explicit
gopkg.in/inf.v0
# gopkg.in/natefinch/lumberjack.v2 v2.2.1
## explicit; go 1.13
gopkg.in/natefinch/lumberjack.v2
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
# k8s.io/api v0.0.0 => ./staging/src/k8s.io/api
## explicit; go 1.24.0
# k8s.io/apiextensions-apiserver v0.0.0 => ./staging/src/k8s.io/apiextensions-apiserver
## explicit; go 1.24.0
# k8s.io/apimachinery v0.0.0 => ./staging/src/k8s.io/apimachinery
## explicit; go 1.24.0
# k8s.io/apiserver v0.0.0 => ./staging/src/k8s.io/apiserver
## explicit; go 1.24.0
# k8s.io/cli-runtime v0.0.0 => ./staging/src/k8s.io/cli-runtime
## explicit; go 1.24.0
# k8s.io/client-go v0.0.0 => ./staging/src/k8s.io/client-go
## explicit; go 1.24.0
# k8s.io/cloud-provider v0.0.0 => ./staging/src/k8s.io/cloud-provider
## explicit; go 1.24.0
# k8s.io/cluster-bootstrap v0.0.0 => ./staging/src/k8s.io/cluster-bootstrap
## explicit; go 1.24.0
# k8s.io/code-generator v0.0.0 => ./staging/src/k8s.io/code-generator
## explicit; go 1.24.0
# k8s.io/component-base v0.0.0 => ./staging/src/k8s.io/component-base
## explicit; go 1.24.0
# k8s.io/component-helpers v0.0.0 => ./staging/src/k8s.io/component-helpers
## explicit; go 1.24.0
# k8s.io/controller-manager v0.0.0 => ./staging/src/k8s.io/controller-manager
## explicit; go 1.24.0
# k8s.io/cri-api v0.0.0 => ./staging/src/k8s.io/cri-api
## explicit; go 1.24.0
# k8s.io/cri-client v0.0.0 => ./staging/src/k8s.io/cri-client
## explicit; go 1.24.0
# k8s.io/csi-translation-lib v0.0.0 => ./staging/src/k8s.io/csi-translation-lib
## explicit; go 1.24.0
# k8s.io/dynamic-resource-allocation v0.0.0 => ./staging/src/k8s.io/dynamic-resource-allocation
## explicit; go 1.24.0
# k8s.io/endpointslice v0.0.0 => ./staging/src/k8s.io/endpointslice
## explicit; go 1.24.0
# k8s.io/externaljwt v0.0.0 => ./staging/src/k8s.io/externaljwt
## explicit; go 1.24.0
# k8s.io/gengo/v2 v2.0.0-20250604051438-85fd79dbfd9f
## explicit; go 1.20
k8s.io/gengo/v2
k8s.io/gengo/v2/codetags
k8s.io/gengo/v2/generator
k8s.io/gengo/v2/namer
k8s.io/gengo/v2/parser
k8s.io/gengo/v2/parser/tags
k8s.io/gengo/v2/types
# k8s.io/klog/v2 v2.130.1
## explicit; go 1.18
k8s.io/klog/v2
k8s.io/klog/v2/internal/buffer
k8s.io/klog/v2/internal/clock
k8s.io/klog/v2/internal/dbg
k8s.io/klog/v2/internal/serialize
k8s.io/klog/v2/internal/severity
k8s.io/klog/v2/internal/sloghandler
k8s.io/klog/v2/internal/verbosity
k8s.io/klog/v2/ktesting
k8s.io/klog/v2/ktesting/init
k8s.io/klog/v2/test
k8s.io/klog/v2/textlogger
# k8s.io/kms v0.0.0 => ./staging/src/k8s.io/kms
## explicit; go 1.24.0
# k8s.io/kube-aggregator v0.0.0 => ./staging/src/k8s.io/kube-aggregator
## explicit; go 1.24.0
# k8s.io/kube-controller-manager v0.0.0 => ./staging/src/k8s.io/kube-controller-manager
## explicit; go 1.24.0
# k8s.io/kube-openapi v0.0.0-20250710124328-f3f2b991d03b
## explicit; go 1.23
k8s.io/kube-openapi/cmd/openapi-gen
k8s.io/kube-openapi/cmd/openapi-gen/args
k8s.io/kube-openapi/pkg/aggregator
k8s.io/kube-openapi/pkg/builder
k8s.io/kube-openapi/pkg/builder3
k8s.io/kube-openapi/pkg/builder3/util
k8s.io/kube-openapi/pkg/cached
k8s.io/kube-openapi/pkg/common
k8s.io/kube-openapi/pkg/common/restfuladapter
k8s.io/kube-openapi/pkg/generators
k8s.io/kube-openapi/pkg/generators/rules
k8s.io/kube-openapi/pkg/handler
k8s.io/kube-openapi/pkg/handler3
k8s.io/kube-openapi/pkg/internal
k8s.io/kube-openapi/pkg/internal/third_party/go-json-experiment/json
k8s.io/kube-openapi/pkg/internal/third_party/govalidator
k8s.io/kube-openapi/pkg/openapiconv
k8s.io/kube-openapi/pkg/schemaconv
k8s.io/kube-openapi/pkg/schemamutation
k8s.io/kube-openapi/pkg/spec3
k8s.io/kube-openapi/pkg/util
k8s.io/kube-openapi/pkg/util/proto
k8s.io/kube-openapi/pkg/util/proto/testing
k8s.io/kube-openapi/pkg/util/proto/validation
k8s.io/kube-openapi/pkg/util/sets
k8s.io/kube-openapi/pkg/validation/errors
k8s.io/kube-openapi/pkg/validation/spec
k8s.io/kube-openapi/pkg/validation/strfmt
k8s.io/kube-openapi/pkg/validation/strfmt/bson
k8s.io/kube-openapi/pkg/validation/validate
# k8s.io/kube-proxy v0.0.0 => ./staging/src/k8s.io/kube-proxy
## explicit; go 1.24.0
# k8s.io/kube-scheduler v0.0.0 => ./staging/src/k8s.io/kube-scheduler
## explicit; go 1.24.0
# k8s.io/kubectl v0.0.0 => ./staging/src/k8s.io/kubectl
## explicit; go 1.24.0
# k8s.io/kubelet v0.0.0 => ./staging/src/k8s.io/kubelet
## explicit; go 1.24.0
# k8s.io/metrics v0.0.0 => ./staging/src/k8s.io/metrics
## explicit; go 1.24.0
# k8s.io/mount-utils v0.0.0 => ./staging/src/k8s.io/mount-utils
## explicit; go 1.24.0
# k8s.io/pod-security-admission v0.0.0 => ./staging/src/k8s.io/pod-security-admission
## explicit; go 1.24.0
# k8s.io/sample-apiserver v0.0.0 => ./staging/src/k8s.io/sample-apiserver
## explicit; go 1.24.0
# k8s.io/system-validators v1.10.1
## explicit; go 1.16
k8s.io/system-validators/validators
# k8s.io/utils v0.0.0-20250604170112-4c0f3b243397
## explicit; go 1.18
k8s.io/utils/buffer
k8s.io/utils/clock
k8s.io/utils/clock/testing
k8s.io/utils/cpuset
k8s.io/utils/exec
k8s.io/utils/exec/testing
k8s.io/utils/inotify
k8s.io/utils/integer
k8s.io/utils/internal/third_party/forked/golang/golang-lru
k8s.io/utils/internal/third_party/forked/golang/net
k8s.io/utils/io
k8s.io/utils/keymutex
k8s.io/utils/lru
k8s.io/utils/net
k8s.io/utils/path
k8s.io/utils/ptr
k8s.io/utils/strings
k8s.io/utils/trace
# sigs.k8s.io/apiserver-network-proxy/konnectivity-client v0.31.2
## explicit; go 1.21
sigs.k8s.io/apiserver-network-proxy/konnectivity-client/pkg/client
sigs.k8s.io/apiserver-network-proxy/konnectivity-client/pkg/client/metrics
sigs.k8s.io/apiserver-network-proxy/konnectivity-client/pkg/common/metrics
sigs.k8s.io/apiserver-network-proxy/konnectivity-client/proto/client
# sigs.k8s.io/json v0.0.0-20241014173422-cfa47c3a1cc8
## explicit; go 1.23
sigs.k8s.io/json
sigs.k8s.io/json/internal/golang/encoding/json
# sigs.k8s.io/knftables v0.0.17
## explicit; go 1.20
sigs.k8s.io/knftables
# sigs.k8s.io/kustomize/api v0.20.1
## explicit; go 1.22.7
sigs.k8s.io/kustomize/api/filters/annotations
sigs.k8s.io/kustomize/api/filters/fieldspec
sigs.k8s.io/kustomize/api/filters/filtersutil
sigs.k8s.io/kustomize/api/filters/fsslice
sigs.k8s.io/kustomize/api/filters/iampolicygenerator
sigs.k8s.io/kustomize/api/filters/imagetag
sigs.k8s.io/kustomize/api/filters/labels
sigs.k8s.io/kustomize/api/filters/nameref
sigs.k8s.io/kustomize/api/filters/namespace
sigs.k8s.io/kustomize/api/filters/patchjson6902
sigs.k8s.io/kustomize/api/filters/patchstrategicmerge
sigs.k8s.io/kustomize/api/filters/prefix
sigs.k8s.io/kustomize/api/filters/refvar
sigs.k8s.io/kustomize/api/filters/replacement
sigs.k8s.io/kustomize/api/filters/replicacount
sigs.k8s.io/kustomize/api/filters/suffix
sigs.k8s.io/kustomize/api/filters/valueadd
sigs.k8s.io/kustomize/api/hasher
sigs.k8s.io/kustomize/api/ifc
sigs.k8s.io/kustomize/api/internal/accumulator
sigs.k8s.io/kustomize/api/internal/builtins
sigs.k8s.io/kustomize/api/internal/generators
sigs.k8s.io/kustomize/api/internal/git
sigs.k8s.io/kustomize/api/internal/image
sigs.k8s.io/kustomize/api/internal/konfig/builtinpluginconsts
sigs.k8s.io/kustomize/api/internal/kusterr
sigs.k8s.io/kustomize/api/internal/loader
sigs.k8s.io/kustomize/api/internal/plugins/builtinconfig
sigs.k8s.io/kustomize/api/internal/plugins/builtinhelpers
sigs.k8s.io/kustomize/api/internal/plugins/execplugin
sigs.k8s.io/kustomize/api/internal/plugins/fnplugin
sigs.k8s.io/kustomize/api/internal/plugins/loader
sigs.k8s.io/kustomize/api/internal/plugins/utils
sigs.k8s.io/kustomize/api/internal/target
sigs.k8s.io/kustomize/api/internal/utils
sigs.k8s.io/kustomize/api/internal/validate
sigs.k8s.io/kustomize/api/konfig
sigs.k8s.io/kustomize/api/krusty
sigs.k8s.io/kustomize/api/kv
sigs.k8s.io/kustomize/api/provenance
sigs.k8s.io/kustomize/api/provider
sigs.k8s.io/kustomize/api/resmap
sigs.k8s.io/kustomize/api/resource
sigs.k8s.io/kustomize/api/types
# sigs.k8s.io/kustomize/kustomize/v5 v5.7.1
## explicit; go 1.22.7
sigs.k8s.io/kustomize/kustomize/v5/commands/build
# sigs.k8s.io/kustomize/kyaml v0.20.1
## explicit; go 1.22.7
sigs.k8s.io/kustomize/kyaml/comments
sigs.k8s.io/kustomize/kyaml/errors
sigs.k8s.io/kustomize/kyaml/ext
sigs.k8s.io/kustomize/kyaml/fieldmeta
sigs.k8s.io/kustomize/kyaml/filesys
sigs.k8s.io/kustomize/kyaml/fn/runtime/container
sigs.k8s.io/kustomize/kyaml/fn/runtime/exec
sigs.k8s.io/kustomize/kyaml/fn/runtime/runtimeutil
sigs.k8s.io/kustomize/kyaml/kio
sigs.k8s.io/kustomize/kyaml/kio/kioutil
sigs.k8s.io/kustomize/kyaml/openapi
sigs.k8s.io/kustomize/kyaml/openapi/kubernetesapi
sigs.k8s.io/kustomize/kyaml/openapi/kubernetesapi/v1_21_2
sigs.k8s.io/kustomize/kyaml/openapi/kustomizationapi
sigs.k8s.io/kustomize/kyaml/order
sigs.k8s.io/kustomize/kyaml/resid
sigs.k8s.io/kustomize/kyaml/runfn
sigs.k8s.io/kustomize/kyaml/sets
sigs.k8s.io/kustomize/kyaml/sliceutil
sigs.k8s.io/kustomize/kyaml/utils
sigs.k8s.io/kustomize/kyaml/yaml
sigs.k8s.io/kustomize/kyaml/yaml/internal/k8sgen/pkg/labels
sigs.k8s.io/kustomize/kyaml/yaml/internal/k8sgen/pkg/selection
sigs.k8s.io/kustomize/kyaml/yaml/internal/k8sgen/pkg/util/errors
sigs.k8s.io/kustomize/kyaml/yaml/internal/k8sgen/pkg/util/sets
sigs.k8s.io/kustomize/kyaml/yaml/internal/k8sgen/pkg/util/validation
sigs.k8s.io/kustomize/kyaml/yaml/internal/k8sgen/pkg/util/validation/field
sigs.k8s.io/kustomize/kyaml/yaml/merge2
sigs.k8s.io/kustomize/kyaml/yaml/schema
sigs.k8s.io/kustomize/kyaml/yaml/walk
# sigs.k8s.io/randfill v1.0.0
## explicit; go 1.18
sigs.k8s.io/randfill
sigs.k8s.io/randfill/bytesource
# sigs.k8s.io/structured-merge-diff/v6 v6.3.0
## explicit; go 1.23
sigs.k8s.io/structured-merge-diff/v6/fieldpath
sigs.k8s.io/structured-merge-diff/v6/merge
sigs.k8s.io/structured-merge-diff/v6/schema
sigs.k8s.io/structured-merge-diff/v6/typed
sigs.k8s.io/structured-merge-diff/v6/value
# sigs.k8s.io/yaml v1.6.0
## explicit; go 1.22
sigs.k8s.io/yaml
sigs.k8s.io/yaml/kyaml
